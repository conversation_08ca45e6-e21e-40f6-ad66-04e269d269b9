<?php

namespace Database\Seeders;

use App\Models\Api\Core\Wilaya;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class Wilayas extends Seeder
{
    public $wilayas = [
        '<PERSON><PERSON>',
        'Chle<PERSON>',
        '<PERSON><PERSON><PERSON><PERSON>',
        'Oum El Bouaghi',
        '<PERSON><PERSON>',
        '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON>rasset',
        '<PERSON><PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON><PERSON>',
        '<PERSON>iar<PERSON>',
        'Tizi <PERSON>uz<PERSON>',
        'Algiers',
        '<PERSON><PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON>',
        '<PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON>',
        '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        'Mostagan<PERSON>',
        '<PERSON>\'Sila',
        '<PERSON><PERSON>ra',
        '<PERSON><PERSON>rg<PERSON>',
        '<PERSON><PERSON>',
        'El Bayadh',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON> Arréridj',
        '<PERSON><PERSON><PERSON><PERSON>',
        '<PERSON> Tarf',
        '<PERSON><PERSON><PERSON>',
        '<PERSON>isse<PERSON><PERSON>',
        '<PERSON>',
        '<PERSON><PERSON><PERSON><PERSON>',
        '<PERSON>uk <PERSON>',
        '<PERSON><PERSON><PERSON><PERSON>',
        '<PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        'Ghardaïa',
        'Relizane',
        'Timimoun',
        'Bordj Badji Mokhtar',
        'Ouled Djellal',
        'Béni Abbès',
        'In Salah',
        'In Guezzam',
        'Touggourt',
        'Djanet',
        'El M\'Ghair',
        'El Meniaa',
    ];

    public function run(): void
    {
        foreach ($this->wilayas as $wilaya) {
            $w = Wilaya::create([
                'name' => $wilaya
            ]);
            $w->baladiyas()->create([
                'name' => $wilaya
            ]);
        }
    }
}
