<?php

namespace App\Http\Controllers\Api\Core;

use App\Http\Controllers\Controller;
use App\Models\Api\Core\Department;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class DepartmentsController extends Controller
{
    public function index()
    {
        $departments = Department::with(['years.sections.groups'])->get();
        return response()->json(['departments' => $departments]);
    }

    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:departments,name',
            ]);

            $department = Department::create($validated);

            return response()->json($department, 201);
        } catch (ValidationException $e) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create department'
            ], 500);
        }
    }

    public function show(Department $department)
    {
        return response()->json($department->load(['years.sections.groups']));
    }

    public function update(Request $request, Department $department)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:departments,name,' . $department->id,
            ]);

            $department->update($validated);

            return response()->json($department);
        } catch (ValidationException $e) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update department'
            ], 500);
        }
    }

    public function destroy(Department $department)
    {
        try {
            $department->delete();
            return response()->json(['message' => 'Department deleted successfully']);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete department'
            ], 500);
        }
    }
}
