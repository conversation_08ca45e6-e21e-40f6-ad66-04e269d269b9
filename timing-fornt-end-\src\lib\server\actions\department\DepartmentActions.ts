'use server'

import axiosInstance from '@/lib/server/tools/axios'
import { DepartmentResponse, Department } from '../../types/departments/allDepartments'
import { revalidatePath } from 'next/cache'

export interface CreateDepartmentRequest {
    name: string;
}

export interface DepartmentErrorResponse {
    message: string;
    errors?: {
        name?: string[];
    };
}

export async function getAllDepartments(): Promise<DepartmentResponse> {
    try {
        const { data } = await axiosInstance.get<DepartmentResponse>(
            `/allDepartments`,
        )
        return data
    } catch (error: any) {
        console.error('Error fetching departments:', error.response?.data)
        throw error
    }
}

export async function createDepartment(departmentData: CreateDepartmentRequest): Promise<Department | DepartmentErrorResponse> {
    try {
        console.log('Creating department with data:', departmentData);
        console.log('API URL:', `${process.env.NEXT_PUBLIC_BACKEND_URL}/departments`);

        const { data } = await axiosInstance.post<Department>(
            `/departments`,
            departmentData
        )
        console.log('Department created successfully:', data);
        revalidatePath('/dashboard')
        return data
    } catch (error: any) {
        console.error('Error creating department:', error.response?.data || error.message);
        console.error('Error status:', error.response?.status);
        console.error('Error config:', error.config);
        if (error.response?.data) {
            return error.response.data as DepartmentErrorResponse
        }
        throw error
    }
}
