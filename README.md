# Timing Table Application

A comprehensive timing table management system built with <PERSON><PERSON> (backend) and Next.js (frontend).

## Project Structure

```
├── TimingTable/          # Laravel Backend API
├── timing-fornt-end-/    # Next.js Frontend
├── start_applications.ps1 # PowerShell startup script
├── start_applications.bat # Batch startup script
└── README.md
```

## Features

- **User Management**: Admin, Teacher, and Student roles
- **Timetable Management**: Create and manage class schedules
- **Multi-language Support**: Arabic, English, and French
- **Responsive Design**: Works on desktop and mobile devices
- **API-driven**: RESTful API with authentication

## Quick Start

### Option 1: Using Startup Scripts (Recommended)

**For PowerShell:**
```powershell
powershell -ExecutionPolicy Bypass -File start_applications.ps1
```

**For Command Prompt:**
```cmd
start_applications.bat
```

### Option 2: Manual Setup

**Backend (Laravel):**
```bash
cd TimingTable
php setup_database.php
php artisan serve --host=127.0.0.1 --port=8000
```

**Frontend (Next.js):**
```bash
cd timing-fornt-end-
npm run dev
```

## Access URLs

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/api

## Database Structure

The application uses SQLite database with the following main tables:

- `users` - User authentication
- `admins` - Administrator accounts
- `teachers` - Teacher profiles
- `students` - Student profiles
- `departments` - Academic departments
- `modules` - Course modules
- `time_tables` - Timetable entries
- `lessens` - Individual lessons
- `days` - Days of the week
- `class_romes` - Classroom information

## Default Credentials

The system will be set up with sample data. Check the database seeders for default login credentials.

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/logout` - User logout

### Core Resources
- `GET /api/departments` - List departments
- `GET /api/modules` - List modules
- `GET /api/teachers` - List teachers
- `GET /api/students` - List students

### Timetable Management
- `GET /api/groups/{group}/time-table` - Get group timetable
- `GET /api/teachers/{teacher}/time-table` - Get teacher timetable
- `GET /api/students/{student}/time-table` - Get student timetable

## Development

### Backend Requirements
- PHP 8.1+
- Composer
- SQLite

### Frontend Requirements
- Node.js 18+
- npm or yarn

### Environment Configuration

**Backend (.env):**
- Database is configured for SQLite
- API runs on port 8000

**Frontend:**
- Configured for development on port 3000
- API base URL: http://localhost:8000

## Troubleshooting

1. **Database Issues**: Run `php setup_database.php` to recreate the database
2. **Port Conflicts**: Change ports in the startup scripts if needed
3. **Permission Issues**: Run PowerShell as Administrator if needed

## Support

For issues and questions, please check the application logs:
- Laravel logs: `TimingTable/storage/logs/`
- Next.js console output in the terminal
