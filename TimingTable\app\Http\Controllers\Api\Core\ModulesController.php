<?php

namespace App\Http\Controllers\Api\Core;

use App\Http\Controllers\Controller;
use App\Models\Api\Core\Module;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class ModulesController extends Controller
{
    public function index()
    {
        $modules = Module::all();
        return response()->json($modules);
    }

    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:modules,name',
            ]);

            $module = Module::create($validated);

            return response()->json($module, 201);
        } catch (ValidationException $e) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create module'
            ], 500);
        }
    }

    public function show(Module $module)
    {
        return response()->json($module);
    }

    public function update(Request $request, Module $module)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:modules,name,' . $module->id,
            ]);

            $module->update($validated);

            return response()->json($module);
        } catch (ValidationException $e) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update module'
            ], 500);
        }
    }

    public function destroy(Module $module)
    {
        try {
            $module->delete();
            return response()->json(['message' => 'Module deleted successfully']);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete module'
            ], 500);
        }
    }
}
