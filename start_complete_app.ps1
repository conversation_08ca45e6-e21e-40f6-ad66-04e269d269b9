Write-Host "=== Timing Table Application Startup ===" -ForegroundColor Green
Write-Host ""

# Step 1: Setup Database
Write-Host "Step 1: Setting up database..." -ForegroundColor Yellow
Set-Location TimingTable
php setup_database.php
Write-Host "Database setup completed!" -ForegroundColor Green
Write-Host ""

# Step 2: Start Laravel Backend
Write-Host "Step 2: Starting Laravel backend on port 8000..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD'; php artisan serve --host=127.0.0.1 --port=8000"
Write-Host "Lara<PERSON> backend starting..." -ForegroundColor Green
Write-Host ""

# Step 3: Wait a moment for backend to start
Write-Host "Step 3: Waiting for backend to initialize..." -ForegroundColor Yellow
Start-Sleep -Seconds 5
Write-Host ""

# Step 4: Start Frontend
Write-Host "Step 4: Starting frontend on port 8080..." -ForegroundColor Yellow
Set-Location ..
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD'; powershell -ExecutionPolicy Bypass -File start_web_server.ps1"
Write-Host "Frontend starting..." -ForegroundColor Green
Write-Host ""

# Step 5: Wait for frontend to start
Write-Host "Step 5: Waiting for frontend to initialize..." -ForegroundColor Yellow
Start-Sleep -Seconds 3
Write-Host ""

# Step 6: Open browser
Write-Host "Step 6: Opening application in browser..." -ForegroundColor Yellow
Start-Process "http://localhost:8080"
Write-Host ""

# Summary
Write-Host "=== Application Started Successfully! ===" -ForegroundColor Green
Write-Host ""
Write-Host "Access URLs:" -ForegroundColor Cyan
Write-Host "  Frontend: http://localhost:8080" -ForegroundColor White
Write-Host "  Backend:  http://localhost:8000" -ForegroundColor White
Write-Host ""
Write-Host "Features:" -ForegroundColor Cyan
Write-Host "  ✓ Database initialized with sample data" -ForegroundColor White
Write-Host "  ✓ Laravel API backend running" -ForegroundColor White
Write-Host "  ✓ Frontend interface available" -ForegroundColor White
Write-Host "  ✓ Real-time status monitoring" -ForegroundColor White
Write-Host ""
Write-Host "Note: Keep the PowerShell windows open to maintain the servers" -ForegroundColor Yellow
Write-Host ""
Write-Host "Press any key to exit this setup script..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
