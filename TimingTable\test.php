<?php
echo "PHP is working!\n";
echo "PHP Version: " . phpversion() . "\n";
echo "Current directory: " . getcwd() . "\n";
echo "Database file exists: " . (file_exists('database/database.sqlite') ? 'Yes' : 'No') . "\n";

// Test database connection
try {
    $pdo = new PDO('sqlite:database/database.sqlite');
    echo "Database connection: Success\n";
    
    // Test if tables exist
    $tables = $pdo->query("SELECT name FROM sqlite_master WHERE type='table'")->fetchAll(PDO::FETCH_COLUMN);
    echo "Tables found: " . count($tables) . "\n";
    if (count($tables) > 0) {
        echo "Table names: " . implode(', ', $tables) . "\n";
    }
} catch (Exception $e) {
    echo "Database connection failed: " . $e->getMessage() . "\n";
}
?>
