"use client";
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import Button from "@/lib/ui/components/global/Buttons/Button";
import { Input } from "@/lib/ui/components/global/Inputs/inputs";
import { SimpleSelect } from "@/lib/ui/components/global/Inputs/SimpleSelect";
import { getAllDepartments } from "@/lib/server/actions/department/DepartmentActions";
import { createYear } from "@/lib/server/actions/year/yearActions";
import { CheckCircle2, AlertCircle } from "lucide-react";

const createYearSchema = z.object({
  name: z.string().min(1, "Year name is required"),
  department_id: z.string().min(1, "Department is required"),
});

type CreateYearFormData = z.infer<typeof createYearSchema>;

export default function CreateYearForm({ onSuccess }: { onSuccess?: () => void }) {
  const [departments, setDepartments] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<CreateYearFormData>({
    resolver: zodResolver(createYearSchema),
  });

  useEffect(() => {
    getAllDepartments().then((data) => setDepartments(data.departments));
  }, []);

  const onSubmit = async (data: CreateYearFormData) => {
    setError(null);
    setSuccess(false);
    try {
      const response = await createYear({
        name: data.name,
        department_id: +data.department_id,
      });
      console.log('Create year response:', response);
      if (response && (response as any).message) {
        setError((response as any).message);
        return;
      }
      setSuccess(true);
      reset();
      setTimeout(() => {
        onSuccess?.();
      }, 1500);
    } catch (e: any) {
      console.error('Create year error:', e);
      console.error('Error details:', e.response?.data);
      console.error('Error status:', e.response?.status);

      if (e.response?.status === 401) {
        setError("You need to be logged in to create years. Please log in and try again.");
      } else if (e.response?.status === 403) {
        setError("You don't have permission to create years.");
      } else if (e.response?.status === 500) {
        setError("Server error. Please check if the database is running and try again.");
      } else {
        setError(e.response?.data?.message || e.message || "Failed to create year");
      }
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4 w-full max-w-md">
      {error && (
        <div className="flex items-center gap-2 text-red-600 dark:text-red-400 animate-fade-in">
          <AlertCircle size={20} />
          <span>{error}</span>
        </div>
      )}
      {success && (
        <div className="flex items-center gap-2 text-green-600 dark:text-green-400 animate-fade-in">
          <CheckCircle2 size={20} />
          <span>Year created successfully!</span>
        </div>
      )}
      <Input
        label="name"
        title="Year Name"
        placeholder="Enter year name (e.g., First Year, Second Year)"
        error={errors.name?.message}
        register={register}
      />
      <SimpleSelect
        label="department_id"
        title="Department"
        placeholder="Select Department"
        error={errors.department_id?.message}
        register={register}
        options={departments.map((dept) => ({
          value: dept.id.toString(),
          label: dept.name,
        }))}
      />
      <Button
        type="submit"
        mode="filled"
        disabled={isSubmitting}
      >
        {isSubmitting ? "Creating..." : "Create Year"}
      </Button>
    </form>
  );
}
