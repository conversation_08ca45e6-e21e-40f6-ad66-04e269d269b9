@echo off
echo Starting Timing Table Application...

echo.
echo Setting up database...
cd TimingTable
php setup_database.php

echo.
echo Starting Laravel backend on port 8000...
start "Laravel Backend" cmd /k "php artisan serve --host=127.0.0.1 --port=8000"

echo.
echo Waiting for backend to start...
timeout /t 5 /nobreak > nul

echo.
echo Starting frontend on port 3000...
cd ..\timing-fornt-end-
start "Frontend Server" cmd /k "php -S 127.0.0.1:3000 -t public"

echo.
echo Applications are starting...
echo Backend: http://localhost:8000
echo Frontend: http://localhost:3000
echo.
echo Press any key to exit...
pause > nul
