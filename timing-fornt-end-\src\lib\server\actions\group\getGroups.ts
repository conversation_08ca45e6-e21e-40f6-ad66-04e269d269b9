'use server'

import axiosInstance from '@/lib/server/tools/axios'
import { GroupResponse } from '../../types/group/group'

export async function getGroups(page: number = 1, year?: string): Promise<GroupResponse> {
    try {
        let url = `/groups?page=${page}`;
        if (year) {
            url += `&year=${year}`;
        }
        const { data } = await axiosInstance.get<GroupResponse>(url)
        return data
    } catch (error) {
        console.error('Error fetching groups:', error)
        throw error
    }
} 