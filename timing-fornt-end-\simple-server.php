<?php
// Simple PHP server to serve static files
$port = 3000;
$host = '127.0.0.1';
$documentRoot = __DIR__ . '/public';

echo "Starting simple PHP server on http://{$host}:{$port}\n";
echo "Document root: {$documentRoot}\n";
echo "Press Ctrl+C to stop\n\n";

// Start the built-in PHP server
$command = "php -S {$host}:{$port} -t \"{$documentRoot}\"";
echo "Running: {$command}\n";

// Execute the command
passthru($command);
?>
