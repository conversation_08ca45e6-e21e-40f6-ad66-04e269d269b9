Write-Host "Timing Table Application - Status Check" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green
Write-Host ""

# Check if database exists
Write-Host "Checking Database..." -ForegroundColor Yellow
if (Test-Path "TimingTable\database\database.sqlite") {
    Write-Host "✓ Database file exists" -ForegroundColor Green
    $dbSize = (Get-Item "TimingTable\database\database.sqlite").Length
    Write-Host "  Database size: $dbSize bytes" -ForegroundColor Cyan
} else {
    Write-Host "✗ Database file missing" -ForegroundColor Red
    Write-Host "  Run: php setup_database.php" -ForegroundColor Yellow
}

Write-Host ""

# Check if backend is running
Write-Host "Checking Backend (Port 8000)..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000" -TimeoutSec 5 -ErrorAction Stop
    Write-Host "✓ Backend is running" -ForegroundColor Green
    Write-Host "  Status: $($response.StatusCode)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ Backend is not running" -ForegroundColor Red
    Write-Host "  Start with: php artisan serve --host=127.0.0.1 --port=8000" -ForegroundColor Yellow
}

Write-Host ""

# Check if frontend is running
Write-Host "Checking Frontend (Port 3000)..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 5 -ErrorAction Stop
    Write-Host "✓ Frontend is running" -ForegroundColor Green
    Write-Host "  Status: $($response.StatusCode)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ Frontend is not running" -ForegroundColor Red
    Write-Host "  Start with: php -S 127.0.0.1:3000 -t public" -ForegroundColor Yellow
}

Write-Host ""

# Check PHP
Write-Host "Checking PHP..." -ForegroundColor Yellow
try {
    $phpVersion = php -v 2>$null | Select-Object -First 1
    if ($phpVersion) {
        Write-Host "✓ PHP is available" -ForegroundColor Green
        Write-Host "  Version: $phpVersion" -ForegroundColor Cyan
    } else {
        throw "PHP not found"
    }
} catch {
    Write-Host "✗ PHP is not available" -ForegroundColor Red
    Write-Host "  Please install PHP and add it to PATH" -ForegroundColor Yellow
}

Write-Host ""

# Summary
Write-Host "Quick Start Commands:" -ForegroundColor Green
Write-Host "  Full Application: .\start_applications.bat" -ForegroundColor Cyan
Write-Host "  Simple Mode: powershell -ExecutionPolicy Bypass -File start_simple.ps1" -ForegroundColor Cyan
Write-Host ""
Write-Host "Access URLs:" -ForegroundColor Green
Write-Host "  Frontend: http://localhost:3000" -ForegroundColor Cyan
Write-Host "  Backend:  http://localhost:8000" -ForegroundColor Cyan

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
