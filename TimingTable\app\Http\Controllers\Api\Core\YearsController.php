<?php

namespace App\Http\Controllers\Api\Core;

use App\Http\Controllers\Controller;
use App\Models\Api\Main\Year;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class YearsController extends Controller
{
    public function index()
    {
        $years = Year::with(['department', 'sections.groups'])->get();
        return response()->json($years);
    }

    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'department_id' => 'required|exists:departments,id',
            ]);

            $year = Year::create($validated);

            return response()->json($year->load('department'), 201);
        } catch (ValidationException $e) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create year'
            ], 500);
        }
    }

    public function show(Year $year)
    {
        return response()->json($year->load(['department', 'sections.groups']));
    }

    public function update(Request $request, Year $year)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'department_id' => 'required|exists:departments,id',
            ]);

            $year->update($validated);

            return response()->json($year->load('department'));
        } catch (ValidationException $e) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update year'
            ], 500);
        }
    }

    public function destroy(Year $year)
    {
        try {
            $year->delete();
            return response()->json(['message' => 'Year deleted successfully']);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete year'
            ], 500);
        }
    }
}
