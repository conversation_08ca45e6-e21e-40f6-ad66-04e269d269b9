"use client";

import { deleteSession } from "@/lib/server/actions/sectionTiming/SectionTimingActions";
import { TimeTableResponse } from "@/lib/server/types/sectionTiming/sectionTiming";
import Button from "@/lib/ui/components/global/Buttons/Button";
import { DashContent, DashContentAction, DashContenTitle, DashContentTable, TableTd, TableTdMain, TableThead, TableTr } from "@/lib/ui/components/local/Dashboard/DashCrudContent";
import LessenTimingForm from "@/lib/ui/forms/TimingForms/LessenTimingForm";
import { Trash } from "lucide-react";
import { useState } from "react";

const STANDARD_SLOTS = [
  { start: "08:00", end: "09:30" },
  { start: "09:30", end: "11:00" },
  { start: "11:00", end: "12:30" },
  { start: "12:30", end: "14:00" },
  { start: "14:00", end: "15:30" },
  { start: "15:30", end: "17:00" },
];
const DAY_LABELS = {
  sat: "Saturday",
  sun: "Sunday",
  mon: "Monday",
  tue: "Tuesday",
  wed: "Wednesday",
  thu: "Thursday",
};
const DAYS = ["sat", "sun", "mon", "tue", "wed", "thu"];

export default function TimingTable({ data, section }: { data: TimeTableResponse, section: number }) {
    const [timingDate] = useState<TimeTableResponse>(data);
    // Map day names to their data for easy lookup
    const dayMap = {};
    timingDate.timeTable.days.forEach(day => {
      dayMap[day.name.toLowerCase().slice(0,3)] = day;
    });
    return (
        <DashContent>
            <DashContenTitle>
                Timing section {section}
            </DashContenTitle>
            <DashContentAction>
                <LessenTimingForm sectionId={section} />
            </DashContentAction>
            <div className="flex flex-col gap-4">
                <DashContentTable>
                  <TableThead list={["Day", ...STANDARD_SLOTS.map(slot => `${slot.start}–${slot.end}`)]} />
                  <tbody>
                    {DAYS.map(dayKey => {
                      const day = dayMap[dayKey];
                      return (
                        <TableTr key={dayKey}>
                          <TableTdMain value={DAY_LABELS[dayKey]} />
                          {STANDARD_SLOTS.map(slot => {
                            let session = null;
                            if (day) {
                              session = day.lessens.find(
                                l => l.start_time.slice(0,5) === slot.start && l.end_time.slice(0,5) === slot.end
                              );
                            }
                            return (
                              <TableTd key={slot.start}>
                                {session ? (
                                  <div className="flex flex-col gap-0.5 p-1">
                                    <span className="font-semibold text-xs">
                                      {session.module.name}
                                      <span className="ml-1 px-1 py-0.5 rounded bg-blue-100 text-blue-700 text-[10px] font-bold uppercase align-middle">{session.type}</span>
                                    </span>
                                    <span className="text-[11px] text-gray-500">
                                      {session.teacher.name} {session.teacher.last} &bull; class: {session.class_rome.number}
                                    </span>
                                    <Delete session={session.id} />
                                  </div>
                                ) : (
                                  <div>—</div>
                                )}
                              </TableTd>
                            );
                          })}
                        </TableTr>
                      );
                    })}
                  </tbody>
                </DashContentTable>
            </div>
        </DashContent>
    )
}

function Delete({ session }: { session: number }) {
    const [isLoading, setIsLoading] = useState(false);
    const [isSuccess, setIsSuccess] = useState(false);
    const [isError, setIsError] = useState(false);
    const [errorMessage, setErrorMessage] = useState("");
    const [successMessage, setSuccessMessage] = useState("");
    const handleDelete = async () => {
        setIsLoading(true);
        const response = await deleteSession(session);
        setIsLoading(false);
        if (response.success) {
            setIsSuccess(true);
            setSuccessMessage(response.message);
        } else {
            setIsError(true);
            setErrorMessage(response.message);
        }
    }
    return (
        errorMessage ? <span className="text-label-small text-red-500">{errorMessage}</span> : successMessage ? <span className="text-label-small text-green-500">{successMessage}</span> : <Trash size={16} className={`${isLoading ? "animate-spin" : ""} ${isSuccess ? "text-green-500" : ""} ${isError ? "text-red-500" : ""}`} onClick={handleDelete} />
    )
}