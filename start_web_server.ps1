# Simple PowerShell HTTP Server
$port = 8080
$url = "http://localhost:$port/"

Write-Host "Starting PowerShell HTTP Server on port $port" -ForegroundColor Green
Write-Host "Open your browser and go to: $url" -ForegroundColor Cyan
Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Yellow
Write-Host ""

# Create HTTP listener
$listener = New-Object System.Net.HttpListener
$listener.Prefixes.Add($url)
$listener.Start()

Write-Host "Server is running..." -ForegroundColor Green

try {
    while ($listener.IsListening) {
        # Wait for a request
        $context = $listener.GetContext()
        $request = $context.Request
        $response = $context.Response
        
        Write-Host "Request: $($request.HttpMethod) $($request.Url.LocalPath)" -ForegroundColor Cyan
        
        # Serve the index.html file
        $filePath = Join-Path $PSScriptRoot "index.html"
        
        if (Test-Path $filePath) {
            $content = Get-Content $filePath -Raw
            $buffer = [System.Text.Encoding]::UTF8.GetBytes($content)
            
            $response.ContentType = "text/html; charset=utf-8"
            $response.ContentLength64 = $buffer.Length
            $response.StatusCode = 200
            
            $response.OutputStream.Write($buffer, 0, $buffer.Length)
        } else {
            $errorContent = "<h1>404 - File Not Found</h1><p>The file index.html was not found.</p>"
            $buffer = [System.Text.Encoding]::UTF8.GetBytes($errorContent)
            
            $response.ContentType = "text/html; charset=utf-8"
            $response.ContentLength64 = $buffer.Length
            $response.StatusCode = 404
            
            $response.OutputStream.Write($buffer, 0, $buffer.Length)
        }
        
        $response.Close()
    }
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    $listener.Stop()
    Write-Host "Server stopped." -ForegroundColor Yellow
}
