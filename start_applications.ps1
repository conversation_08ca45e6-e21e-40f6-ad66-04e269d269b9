Write-Host "Starting Timing Table Application..." -ForegroundColor Green

Write-Host ""
Write-Host "Setting up database..." -ForegroundColor Yellow
Set-Location TimingTable
php setup_database.php

Write-Host ""
Write-Host "Starting Laravel backend on port 8000..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "php artisan serve --host=127.0.0.1 --port=8000"

Write-Host ""
Write-Host "Waiting for backend to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

Write-Host ""
Write-Host "Starting Next.js frontend on port 3000..." -ForegroundColor Yellow
Set-Location ..\timing-fornt-end-
Start-Process powershell -ArgumentList "-NoExit", "-Command", "npm run dev"

Write-Host ""
Write-Host "Applications are starting..." -ForegroundColor Green
Write-Host "Backend: http://localhost:8000" -ForegroundColor Cyan
Write-Host "Frontend: http://localhost:3000" -ForegroundColor Cyan
Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
