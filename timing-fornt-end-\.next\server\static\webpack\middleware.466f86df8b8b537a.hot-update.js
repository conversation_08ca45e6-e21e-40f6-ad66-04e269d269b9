"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_intl_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/middleware */ \"(middleware)/./node_modules/next-intl/dist/esm/development/middleware/middleware.js\");\n/* harmony import */ var _i18n_routing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./i18n/routing */ \"(middleware)/./src/i18n/routing.ts\");\n\n\n// Define protected and public routes\nconst protectedRoutes = [\n    '/dashboard',\n    '/logout'\n];\nconst publicRoutes = [\n    '/',\n    '/register'\n];\nasync function middleware(request) {\n    // Handle internationalization first\n    const intlMiddleware = (0,next_intl_middleware__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_i18n_routing__WEBPACK_IMPORTED_MODULE_0__.routing);\n    const intlResponse = await intlMiddleware(request);\n    // For now, just return the intl response to get the app working\n    // We'll add auth logic later once the basic app is running\n    return intlResponse;\n/* TODO: Re-enable auth middleware once app is stable\r\n  const pathname = request.nextUrl.pathname;\r\n  const locale = pathname.split('/')[1];\r\n\r\n  // Simple auth check using cookies instead of server functions\r\n  const sessionCookie = request.cookies.get('session');\r\n  const isAuthenticated = !!sessionCookie?.value;\r\n\r\n  const isProtectedRoute = protectedRoutes.some(route => pathname === `/${locale}${route}`);\r\n  const isPublicRoute = publicRoutes.some(route => pathname === `/${locale}${route}`);\r\n\r\n  // If user is not authenticated and trying to access protected route\r\n  if (!isAuthenticated && isProtectedRoute) {\r\n    return NextResponse.redirect(new URL(`/${locale}`, request.url));\r\n  }\r\n\r\n  // If user is authenticated and trying to access public route\r\n  if (isAuthenticated && isPublicRoute) {\r\n    return NextResponse.redirect(new URL(`/${locale}/dashboard`, request.url));\r\n  }\r\n\r\n  return intlResponse;\r\n  */ }\nconst config = {\n    // Match all pathnames except for\n    // - … if they start with `/api`, `/trpc`, `/_next` or `/_vercel`\n    // - … the ones containing a dot (e.g. `favicon.ico`)\n    matcher: '/((?!api|trpc|_next|_vercel|.*\\\\..*).*)'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});