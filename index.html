<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Timing Table Management System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            font-weight: bold;
        }
        
        h1 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 2rem;
        }
        
        p {
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #dee2e6;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .status {
            margin-top: 2rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #28a745;
        }
        
        .status h3 {
            color: #28a745;
            margin-bottom: 0.5rem;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            margin: 0.5rem 0;
        }
        
        .status-ok {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }

        .instructions {
            margin-top: 2rem;
            padding: 1rem;
            background: #fff3cd;
            border-radius: 5px;
            border-left: 4px solid #ffc107;
        }

        .instructions h3 {
            color: #856404;
            margin-bottom: 0.5rem;
        }

        .instructions p {
            color: #856404;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .code {
            background: #f8f9fa;
            padding: 0.5rem;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            margin: 0.5rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">TT</div>
        <h1>Timing Table Management System</h1>
        <p>Welcome to the comprehensive timing table management system. This application helps you manage class schedules, teachers, students, and academic resources efficiently.</p>
        
        <div class="buttons">
            <a href="#" class="btn btn-primary" onclick="showInstructions()">Setup Instructions</a>
            <a href="#" class="btn btn-secondary" onclick="openLaravel()">Test Laravel (Port 8000)</a>
            <a href="#" class="btn btn-secondary" onclick="openDashboard()">Dashboard Demo</a>
        </div>
        
        <div class="status">
            <h3>System Status</h3>
            <div class="status-item">
                <span>Frontend:</span>
                <span class="status-ok">✓ Running (Static)</span>
            </div>
            <div class="status-item">
                <span>Backend API:</span>
                <span id="backend-status" class="status-error">✗ Not Started</span>
            </div>
            <div class="status-item">
                <span>Database:</span>
                <span id="database-status" class="status-error">✗ Unknown</span>
            </div>
        </div>

        <div class="instructions">
            <h3>Quick Start Instructions</h3>
            <p><strong>Step 1:</strong> Open Command Prompt in the project root directory</p>
            <div class="code">cd "C:\Users\<USER>\Desktop\fin project\Final project\Final project"</div>
            
            <p><strong>Step 2:</strong> Run the startup script</p>
            <div class="code">.\start_applications.bat</div>
            
            <p><strong>Step 3:</strong> Wait for both servers to start, then visit:</p>
            <div class="code">http://localhost:8000 (Laravel Backend)</div>
            
            <p><strong>Alternative:</strong> Start manually</p>
            <div class="code">
                cd TimingTable<br>
                php artisan serve --host=127.0.0.1 --port=8000
            </div>
        </div>
    </div>

    <script>
        function showInstructions() {
            const instructions = `
TIMING TABLE APPLICATION - SETUP INSTRUCTIONS

OPTION 1 - Quick Start (Recommended):
1. Open Command Prompt as Administrator
2. Navigate to project directory:
   cd "C:\\Users\\<USER>\\Desktop\\fin project\\Final project\\Final project"
3. Run: powershell -ExecutionPolicy Bypass -File start_complete_app.ps1

OPTION 2 - Manual Setup:
1. Start Backend:
   cd TimingTable
   php -S 127.0.0.1:8000 -t public

2. Start Frontend (already running on port 8080)

OPTION 3 - Laravel Artisan (if available):
   cd TimingTable
   php artisan serve --host=127.0.0.1 --port=8000

ACCESS URLS:
- Frontend: http://localhost:8080 (current page)
- Backend: http://localhost:8000
- API: http://localhost:8000/api

TROUBLESHOOTING:
- If PHP commands don't work, install PHP and add to PATH
- If ports are busy, change port numbers in commands
- Database is SQLite, no additional setup needed
            `;
            alert(instructions);
        }

        function openLaravel() {
            window.open('http://localhost:8000', '_blank');
        }

        function openDashboard() {
            // Create a simple dashboard demo
            const dashboardWindow = window.open('', '_blank');
            dashboardWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Timing Table Dashboard</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
                        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
                        .card { background: white; padding: 20px; border-radius: 10px; margin: 10px 0; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
                        .btn { background: #667eea; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
                        .btn:hover { background: #5a6fd8; }
                        table { width: 100%; border-collapse: collapse; }
                        th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
                        th { background: #f8f9fa; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>📅 Timing Table Management System</h1>
                        <p>Dashboard Demo - Academic Schedule Management</p>
                    </div>

                    <div class="grid">
                        <div class="card">
                            <h3>📊 Quick Stats</h3>
                            <p><strong>Total Classes:</strong> 12</p>
                            <p><strong>Active Teachers:</strong> 8</p>
                            <p><strong>Students Enrolled:</strong> 245</p>
                            <p><strong>Departments:</strong> 3</p>
                        </div>

                        <div class="card">
                            <h3>🕒 Today's Schedule</h3>
                            <table>
                                <tr><th>Time</th><th>Subject</th><th>Room</th></tr>
                                <tr><td>09:00</td><td>Mathematics</td><td>A101</td></tr>
                                <tr><td>10:30</td><td>Physics</td><td>B202</td></tr>
                                <tr><td>14:00</td><td>Programming</td><td>C301</td></tr>
                            </table>
                        </div>

                        <div class="card">
                            <h3>👥 Recent Activity</h3>
                            <p>• New timetable created for Class 10A</p>
                            <p>• Teacher John Doe assigned to Physics</p>
                            <p>• Room B202 scheduled for maintenance</p>
                        </div>

                        <div class="card">
                            <h3>⚙️ Quick Actions</h3>
                            <button class="btn" onclick="alert('Feature: Create new timetable')">Create Timetable</button><br><br>
                            <button class="btn" onclick="alert('Feature: Manage teachers')">Manage Teachers</button><br><br>
                            <button class="btn" onclick="alert('Feature: View reports')">View Reports</button>
                        </div>
                    </div>

                    <div class="card">
                        <h3>📋 Weekly Overview</h3>
                        <table>
                            <tr><th>Day</th><th>Classes</th><th>Teachers</th><th>Rooms Used</th></tr>
                            <tr><td>Monday</td><td>15</td><td>8</td><td>12</td></tr>
                            <tr><td>Tuesday</td><td>18</td><td>9</td><td>14</td></tr>
                            <tr><td>Wednesday</td><td>16</td><td>7</td><td>11</td></tr>
                            <tr><td>Thursday</td><td>17</td><td>8</td><td>13</td></tr>
                            <tr><td>Friday</td><td>14</td><td>6</td><td>10</td></tr>
                        </table>
                    </div>
                </body>
                </html>
            `);
        }
        
        // Check backend status periodically
        function checkBackend() {
            fetch('http://localhost:8000')
                .then(response => {
                    if (response.ok) {
                        document.getElementById('backend-status').innerHTML = '✓ Running';
                        document.getElementById('backend-status').className = 'status-ok';
                        document.getElementById('database-status').innerHTML = '✓ Connected';
                        document.getElementById('database-status').className = 'status-ok';
                    }
                })
                .catch(error => {
                    document.getElementById('backend-status').innerHTML = '✗ Offline';
                    document.getElementById('backend-status').className = 'status-error';
                });
        }
        
        // Check every 5 seconds
        setInterval(checkBackend, 5000);
        
        // Initial check
        setTimeout(checkBackend, 1000);
    </script>
</body>
</html>
