"use client";

import Button from "@/lib/ui/components/global/Buttons/Button";
import { Plus } from "lucide-react";
import { useState } from "react";
import Dialog from "@/lib/ui/components/global/Dialog/Dialog";
import CreateTeacherForm from "@/lib/ui/forms/teacher/CreateTeacherForm";

export function TeacherDialog() {
    const [isDialogOpen, setIsDialogOpen] = useState(false);

    const handleOpenDialog = () => {
        setIsDialogOpen(true);
    };

    const handleCloseDialog = () => {
        setIsDialogOpen(false);
    };

    return (
        <>
            <Button
                mode="filled"
                onClick={handleOpenDialog}
                icon={<Plus />}
            >
                Create Teacher
            </Button>
            <Dialog
                isOpen={isDialogOpen}
                onClose={handleCloseDialog}
                title="Create New Teacher"
            >
                <CreateTeacherForm onSuccess={handleCloseDialog} />
            </Dialog>
        </>
    );
} 