"use client";

import { Search } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { Input } from "@/lib/ui/components/global/Inputs/inputs";
import Button from "@/lib/ui/components/global/Buttons/Button";

interface SearchFormData {
    search: string;
}

export default function SearchTeachers() {
    const router = useRouter();
    const searchParams = useSearchParams();
    const { register, handleSubmit } = useForm<SearchFormData>({
        defaultValues: {
            search: searchParams.get("search") || "",
        },
    });

    const onSubmit = (data: SearchFormData) => {
        const params = new URLSearchParams(searchParams.toString());
        if (data.search) {
            params.set("search", data.search);
        } else {
            params.delete("search");
        }
        params.set("page", "1"); // Reset to first page when searching
        router.push(`/dashboard/teachers?${params.toString()}`);
    };

    return (
        <form onSubmit={handleSubmit(onSubmit)} className="flex items-center gap-2">
            <Input
                type="text"
                placeholder="Search teachers..."
                label="search"
                register={register}
                className="w-64"
            />
            <Button type="submit" mode="filled" icon={<Search />}>
                Search
            </Button>
        </form>
    );
} 