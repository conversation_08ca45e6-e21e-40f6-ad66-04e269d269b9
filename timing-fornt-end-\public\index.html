<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Timing Table Management System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            font-weight: bold;
        }
        
        h1 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 2rem;
        }
        
        p {
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #dee2e6;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .status {
            margin-top: 2rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #28a745;
        }
        
        .status h3 {
            color: #28a745;
            margin-bottom: 0.5rem;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            margin: 0.5rem 0;
        }
        
        .status-ok {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">TT</div>
        <h1>Timing Table Management System</h1>
        <p>Welcome to the comprehensive timing table management system. This application helps you manage class schedules, teachers, students, and academic resources efficiently.</p>
        
        <div class="buttons">
            <a href="#" class="btn btn-primary" onclick="checkBackend()">Login</a>
            <a href="#" class="btn btn-secondary" onclick="viewTimetable()">View Timetable</a>
        </div>
        
        <div class="status">
            <h3>System Status</h3>
            <div class="status-item">
                <span>Frontend:</span>
                <span class="status-ok">✓ Running</span>
            </div>
            <div class="status-item">
                <span>Backend API:</span>
                <span id="backend-status" class="status-error">✗ Checking...</span>
            </div>
            <div class="status-item">
                <span>Database:</span>
                <span id="database-status" class="status-error">✗ Checking...</span>
            </div>
        </div>
    </div>

    <script>
        // Check backend status
        async function checkBackend() {
            try {
                const response = await fetch('http://localhost:8000');
                if (response.ok) {
                    document.getElementById('backend-status').innerHTML = '✓ Connected';
                    document.getElementById('backend-status').className = 'status-ok';
                    
                    // If backend is working, redirect to the actual Next.js app
                    window.location.href = 'http://localhost:3000';
                } else {
                    throw new Error('Backend not responding');
                }
            } catch (error) {
                document.getElementById('backend-status').innerHTML = '✗ Offline';
                document.getElementById('backend-status').className = 'status-error';
                alert('Backend server is not running. Please start the Laravel server first.');
            }
        }
        
        async function viewTimetable() {
            alert('This feature will be available once the full application is running.');
        }
        
        // Auto-check backend status on page load
        window.onload = function() {
            setTimeout(checkBackend, 1000);
        };
    </script>
</body>
</html>
