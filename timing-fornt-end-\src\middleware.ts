import createMiddleware from 'next-intl/middleware';
import { routing } from './i18n/routing';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Define protected and public routes
const protectedRoutes = ['/dashboard', '/logout'];
const publicRoutes = ['/', '/register'];

export async function middleware(request: NextRequest) {
  // Handle internationalization first
  const intlMiddleware = createMiddleware(routing);
  const intlResponse = await intlMiddleware(request);

  // For now, just return the intl response to get the app working
  // We'll add auth logic later once the basic app is running
  return intlResponse;

  /* TODO: Re-enable auth middleware once app is stable
  const pathname = request.nextUrl.pathname;
  const locale = pathname.split('/')[1];

  // Simple auth check using cookies instead of server functions
  const sessionCookie = request.cookies.get('session');
  const isAuthenticated = !!sessionCookie?.value;

  const isProtectedRoute = protectedRoutes.some(route => pathname === `/${locale}${route}`);
  const isPublicRoute = publicRoutes.some(route => pathname === `/${locale}${route}`);

  // If user is not authenticated and trying to access protected route
  if (!isAuthenticated && isProtectedRoute) {
    return NextResponse.redirect(new URL(`/${locale}`, request.url));
  }

  // If user is authenticated and trying to access public route
  if (isAuthenticated && isPublicRoute) {
    return NextResponse.redirect(new URL(`/${locale}/dashboard`, request.url));
  }

  return intlResponse;
  */
}

export const config = {
  // Match all pathnames except for
  // - … if they start with `/api`, `/trpc`, `/_next` or `/_vercel`
  // - … the ones containing a dot (e.g. `favicon.ico`)
  matcher: '/((?!api|trpc|_next|_vercel|.*\\..*).*)'
};