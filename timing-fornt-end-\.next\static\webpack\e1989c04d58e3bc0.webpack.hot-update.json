{"c": ["app/layout", "app/[locale]/(Guest)/page", "app/[locale]/(Dashboard)/dashboard/(main)/page", "app/[locale]/(Dashboard)/dashboard/(main)/students/page", "app/[locale]/(Dashboard)/dashboard/(main)/teachers/page", "app/[locale]/(Guest)/(Auth)/register/page", "app/[locale]/(Dashboard)/dashboard/(main)/students/create/page", "app/[locale]/(Dashboard)/dashboard/(main)/teachers/create/page", "app/[locale]/teacher/groups-timetables/page", "app/[locale]/(Dashboard)/dashboard/(timing)/groups/page", "app/[locale]/(Dashboard)/dashboard/(main)/create/page", "app/[locale]/(Dashboard)/dashboard/(timing)/sections/page", "app/[locale]/(Dashboard)/dashboard/(core)/years/page", "app/[locale]/(Dashboard)/dashboard/(core)/modules/page", "app/[locale]/(Dashboard)/dashboard/(main)/students/[student]/page", "app/[locale]/(Dashboard)/dashboard/(timing)/groups/timing/[group]/page", "app/[locale]/(Dashboard)/dashboard/(timing)/sections/timing/[section]/page", "app/[locale]/(Dashboard)/dashboard/(core)/departements/page", "webpack"], "r": ["app/_not-found/page"], "m": ["(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cpro%5CDesktop%5Cfin%20project%5CFinal%20project%5CFinal%20project%5Ctiming-fornt-end-%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-error.js&page=%2F_not-found%2Fpage!", "(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-fallback.js", "(app-pages-browser)/./node_modules/next/dist/client/components/not-found-error.js"]}