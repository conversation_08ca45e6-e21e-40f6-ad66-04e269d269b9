"use client";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import Button from "@/lib/ui/components/global/Buttons/Button";
import { Input } from "@/lib/ui/components/global/Inputs/inputs";
import { createDepartment } from "@/lib/server/actions/department/DepartmentActions";
import { CheckCircle2, AlertCircle } from "lucide-react";

const createDepartmentSchema = z.object({
  name: z.string().min(1, "Department name is required"),
});

type CreateDepartmentFormData = z.infer<typeof createDepartmentSchema>;

export default function CreateDepartmentForm({ onSuccess }: { onSuccess?: () => void }) {
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<CreateDepartmentFormData>({
    resolver: zodResolver(createDepartmentSchema),
  });

  const onSubmit = async (data: CreateDepartmentFormData) => {
    setError(null);
    setSuccess(false);
    try {
      console.log('Submitting department data:', data);
      const response = await createDepartment({ name: data.name });
      console.log('Create department response:', response);
      console.log('Response type:', typeof response);
      console.log('Response keys:', Object.keys(response || {}));

      if (response && (response as any).message && !(response as any).id) {
        setError((response as any).message);
        return;
      }
      setSuccess(true);
      reset();
      setTimeout(() => {
        onSuccess?.();
      }, 1500);
    } catch (e: any) {
      console.error('Create department error:', e);
      console.error('Error details:', e.response?.data);
      console.error('Error status:', e.response?.status);

      if (e.response?.status === 401) {
        setError("You need to be logged in to create departments. Please log in and try again.");
      } else if (e.response?.status === 403) {
        setError("You don't have permission to create departments.");
      } else if (e.response?.status === 500) {
        setError("Server error. Please check if the database is running and try again.");
      } else {
        setError(e.response?.data?.message || e.message || "Failed to create department");
      }
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4 w-full max-w-md">
      {error && (
        <div className="flex items-center gap-2 text-red-600 dark:text-red-400 animate-fade-in">
          <AlertCircle size={20} />
          <span>{error}</span>
        </div>
      )}
      {success && (
        <div className="flex items-center gap-2 text-green-600 dark:text-green-400 animate-fade-in">
          <CheckCircle2 size={20} />
          <span>Department created successfully!</span>
        </div>
      )}
      <Input
        label="name"
        title="Department Name"
        placeholder="Enter department name (e.g., Computer Science, Mathematics)"
        error={errors.name?.message}
        register={register}
      />
      <Button
        type="submit"
        mode="filled"
        disabled={isSubmitting}
      >
        {isSubmitting ? "Creating..." : "Create Department"}
      </Button>
    </form>
  );
}