<?php
// Simple PHP server for Laravel
$host = '127.0.0.1';
$port = 8000;
$documentRoot = __DIR__ . '/public';

echo "Starting Laravel backend server...\n";
echo "Host: $host\n";
echo "Port: $port\n";
echo "Document Root: $documentRoot\n";
echo "URL: http://$host:$port\n";
echo "\nPress Ctrl+C to stop the server\n\n";

// Start the built-in PHP server
$command = "php -S $host:$port -t \"$documentRoot\"";
echo "Running: $command\n\n";

// Execute the command
passthru($command);
?>
