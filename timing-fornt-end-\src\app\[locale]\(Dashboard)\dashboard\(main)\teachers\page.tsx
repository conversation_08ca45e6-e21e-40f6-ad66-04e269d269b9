import { getTeachers } from "@/lib/server/actions/teacher/getTeachers";
import DashSection from "@/lib/ui/components/global/Section/Section";
import TeacherPagination from "@/lib/ui/components/local/Dashboard/Teacher/TeacherPagination";
import TeachersTable from "@/lib/ui/components/local/Dashboard/Teacher/TeachersTable";
import TeacherStat from "@/lib/ui/components/local/Dashboard/Teacher/TeacherStat";
import { DashContentAction, DashContenTitle, DashContentPaginationSkeleton, DashContentStatItemSkeleton, DashContentTableSkeleton } from "@/lib/ui/components/local/Dashboard/DashCrudContent";
import { Suspense } from "react";
import SearchTeachers from "./SearchTeachers";
import { TeacherDialog } from "./TeacherDialog";

interface PageProps {
    searchParams: { page?: string; search?: string }
}

export default async function page({ searchParams }: PageProps) {
    const page = (await searchParams).page || "1";
    const search = (await searchParams).search || "";
    const teachers = await getTeachers(parseInt(page), search);

    return (
        <DashSection>
            <DashContenTitle>Teachers</DashContenTitle>
            <Suspense fallback={<DashContentStatItemSkeleton />}>
                <TeacherStat />
            </Suspense>
            <DashContentAction>
                <div className="flex items-center gap-4">
                    <SearchTeachers />
                    <TeacherDialog />
                </div>
            </DashContentAction>
            <Suspense fallback={<DashContentTableSkeleton />}>
                <TeachersTable page={page} search={search} />
            </Suspense>
            <Suspense fallback={<DashContentPaginationSkeleton />}>
                <TeacherPagination data={teachers} currentPage={parseInt(page)} search={search} />
            </Suspense>
        </DashSection>
    )
}