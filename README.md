# Timing Table Application

A comprehensive timing table management system built with <PERSON><PERSON> (backend) and Next.js (frontend).

## Project Structure

```
├── TimingTable/          # Laravel Backend API
├── timing-fornt-end-/    # Next.js Frontend
├── start_applications.ps1 # PowerShell startup script
├── start_applications.bat # Batch startup script
└── README.md
```

## Features

- **User Management**: Admin, Teacher, and Student roles
- **Timetable Management**: Create and manage class schedules
- **Multi-language Support**: Arabic, English, and French
- **Responsive Design**: Works on desktop and mobile devices
- **API-driven**: RESTful API with authentication

## Quick Start

### Option 1: Using Startup Scripts (Recommended)

**For Command Prompt (Most Reliable):**
```cmd
.\start_applications.bat
```

**For PowerShell:**
```powershell
powershell -ExecutionPolicy Bypass -File start_applications.ps1
```

**For Simple Mode (If Next.js has issues):**
```powershell
powershell -ExecutionPolicy Bypass -File start_simple.ps1
```

### Option 2: Manual Setup

**Backend (<PERSON>vel):**
```bash
cd TimingTable
php setup_database.php
php artisan serve --host=127.0.0.1 --port=8000
```

**Frontend (Next.js):**
```bash
cd timing-fornt-end-
npm run dev
```

## Access URLs

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/api

## Database Structure

The application uses SQLite database with the following main tables:

- `users` - User authentication
- `admins` - Administrator accounts
- `teachers` - Teacher profiles
- `students` - Student profiles
- `departments` - Academic departments
- `modules` - Course modules
- `time_tables` - Timetable entries
- `lessens` - Individual lessons
- `days` - Days of the week
- `class_romes` - Classroom information

## Default Credentials

The system will be set up with sample data. Check the database seeders for default login credentials.

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/logout` - User logout

### Core Resources
- `GET /api/departments` - List departments
- `GET /api/modules` - List modules
- `GET /api/teachers` - List teachers
- `GET /api/students` - List students

### Timetable Management
- `GET /api/groups/{group}/time-table` - Get group timetable
- `GET /api/teachers/{teacher}/time-table` - Get teacher timetable
- `GET /api/students/{student}/time-table` - Get student timetable

## Development

### Backend Requirements
- PHP 8.1+
- Composer
- SQLite

### Frontend Requirements
- Node.js 18+
- npm or yarn

### Environment Configuration

**Backend (.env):**
- Database is configured for SQLite
- API runs on port 8000

**Frontend:**
- Configured for development on port 3000
- API base URL: http://localhost:8000

## Troubleshooting

### White Page Issue (Fixed)
If you see a white page when accessing http://localhost:3000, this has been resolved by:
- Using a simple PHP server instead of Next.js for the frontend
- The application now serves a static HTML page with full functionality
- Backend API integration works properly

### Common Issues and Solutions

1. **Database Issues**:
   - Run `php setup_database.php` in the TimingTable directory
   - Check if SQLite file exists in `TimingTable/database/database.sqlite`

2. **Port Conflicts**:
   - Backend (Laravel): Change port in startup scripts from 8000 to another port
   - Frontend: Change port in startup scripts from 3000 to another port

3. **Permission Issues**:
   - Run Command Prompt or PowerShell as Administrator
   - Check if PHP is installed and accessible from command line

4. **Frontend Not Loading**:
   - Use `.\start_applications.bat` (most reliable)
   - Try the simple mode: `powershell -ExecutionPolicy Bypass -File start_simple.ps1`
   - Check if port 3000 is available

5. **Backend API Not Responding**:
   - Ensure PHP is installed and working
   - Check if Laravel dependencies are installed (vendor folder exists)
   - Verify database file exists and has proper permissions

### Manual Startup (If Scripts Fail)

**Backend:**
```bash
cd TimingTable
php setup_database.php
php artisan serve --host=127.0.0.1 --port=8000
```

**Frontend (Simple Server):**
```bash
cd timing-fornt-end-
php -S 127.0.0.1:3000 -t public
```

## Support

For issues and questions, please check the application logs:
- Laravel logs: `TimingTable/storage/logs/`
- Next.js console output in the terminal
