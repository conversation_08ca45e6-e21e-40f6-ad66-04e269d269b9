import { Dash<PERSON><PERSON><PERSON>Action, DashContentPaginationSkeleton, DashContentStatItemSkeleton, DashContentTableSkeleton } from "@/lib/ui/components/local/Dashboard/DashCrudContent";
import { DashContenTitle } from "@/lib/ui/components/local/Dashboard/DashCrudContent";
import { getGroups } from "@/lib/server/actions/group/getGroups";
import DashSection from "@/lib/ui/components/global/Section/Section";
import { Suspense } from "react";
import Link from "next/link";
import Button from "@/lib/ui/components/global/Buttons/Button";
import GroupStat from "@/lib/ui/components/local/Dashboard/Group/GroupStat";
import GroupsTable from "@/lib/ui/components/local/Dashboard/Group/GroupsTable";
import GroupPagination from "@/lib/ui/components/local/Dashboard/Group/GroupPagination";
import { Plus } from "lucide-react";
import { getAllDepartments } from "@/lib/server/actions/department/DepartmentActions";
import YearFilter from "./YearFilter";
import CreateGroupDialog from "./CreateGroupDialog";

interface PageProps {
    searchParams: { page?: string; year?: string }
}

export default async function page({ searchParams }: PageProps) {
    const page = searchParams.page || "1";
    const selectedYear = searchParams.year || "";
    const groups = await getGroups(parseInt(page), selectedYear);
    const departments = await getAllDepartments();
    // Flatten all years from all departments
    const allYears = departments.departments.flatMap(dep => dep.years.map(year => ({
        ...year,
        departmentName: dep.name
    })));

    return (
        <DashSection>
            <DashContenTitle>Groups</DashContenTitle>
            <Suspense fallback={<DashContentStatItemSkeleton />}>
                <GroupStat />
            </Suspense>
            <DashContentAction>
                <div className="flex items-center gap-4 w-full">
                    <YearFilter allYears={allYears} selectedYear={selectedYear} />
                    <CreateGroupDialog />
                </div>
            </DashContentAction>
            <Suspense fallback={<DashContentTableSkeleton />}>
                <GroupsTable page={page} year={selectedYear} />
            </Suspense>
            <Suspense fallback={<DashContentPaginationSkeleton />}>
                <GroupPagination data={groups} currentPage={parseInt(page)} />
            </Suspense>
        </DashSection>
    )
}