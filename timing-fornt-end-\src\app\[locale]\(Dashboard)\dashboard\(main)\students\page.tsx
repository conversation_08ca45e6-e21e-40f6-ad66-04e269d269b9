import { getStudents } from "@/lib/server/actions/student/getStudents";
import StudentsTable from "@/lib/ui/components/local/Dashboard/Student/StudentsTable";
import DashSection from "@/lib/ui/components/global/Section/Section";
import { StudentDialog } from "./StudentDialog";
import { DashContentAction, DashContentPaginationSkeleton, DashContentStatItemSkeleton, DashContentTableSkeleton } from "@/lib/ui/components/local/Dashboard/DashCrudContent";
import { Suspense } from "react";
import SearchStudents from "./SearchStudents";
import StudentPagination from "@/lib/ui/components/local/Dashboard/Student/StudentPagination";
import StudentStat from "@/lib/ui/components/local/Dashboard/Student/StudentStat";

interface PageProps {
    searchParams: {
        page?: string;
        search?: string;
    };
}

export default async function StudentsPage({ searchParams }: PageProps) {
    const page = searchParams.page || "1";
    const search = searchParams.search || "";
    const students = await getStudents(parseInt(page), search);

    return (
        <DashSection>
            <Suspense fallback={<DashContentStatItemSkeleton />}>
                <StudentStat />
            </Suspense>
            <div className="mb-6 mt-2">
                <h2 className="text-3xl font-extrabold text-on-background dark:text-dark-on-background mb-4">Students</h2>
                <div className="flex flex-wrap items-center gap-4">
                    <SearchStudents />
                    <StudentDialog />
                </div>
            </div>
            <Suspense fallback={<DashContentTableSkeleton />}>
                <StudentsTable page={page} search={search} />
            </Suspense>
            <Suspense fallback={<DashContentPaginationSkeleton />}>
                <StudentPagination data={students} currentPage={parseInt(page)} search={search} />
            </Suspense>
        </DashSection>
    );
}