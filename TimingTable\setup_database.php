<?php

// Simple script to set up the database manually
require_once 'vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as Capsule;

// Set up database connection
$capsule = new Capsule;
$capsule->addConnection([
    'driver' => 'sqlite',
    'database' => __DIR__ . '/database/database.sqlite',
    'prefix' => '',
]);

$capsule->setAsGlobal();
$capsule->bootEloquent();

try {
    // Create tables
    echo "Creating tables...\n";
    
    // Users table
    if (!Capsule::schema()->hasTable('users')) {
        Capsule::schema()->create('users', function ($table) {
            $table->id();
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');
            $table->rememberToken();
            $table->timestamps();
        });
        echo "Created users table\n";
    }

    // Keys table
    if (!Capsule::schema()->hasTable('keys')) {
        Capsule::schema()->create('keys', function ($table) {
            $table->id();
            $table->string('value');
            $table->morphs('keyable');
            $table->timestamps();
        });
        echo "Created keys table\n";
    }

    // Wilayas table
    if (!Capsule::schema()->hasTable('wilayas')) {
        Capsule::schema()->create('wilayas', function ($table) {
            $table->id();
            $table->string('name');
            $table->timestamps();
        });
        echo "Created wilayas table\n";
    }

    // Baladiyas table
    if (!Capsule::schema()->hasTable('baladiyas')) {
        Capsule::schema()->create('baladiyas', function ($table) {
            $table->id();
            $table->string('name');
            $table->timestamps();
        });
        echo "Created baladiyas table\n";
    }

    // Departments table
    if (!Capsule::schema()->hasTable('departments')) {
        Capsule::schema()->create('departments', function ($table) {
            $table->id();
            $table->string('name');
            $table->timestamps();
        });
        echo "Created departments table\n";
    }

    // Years table
    if (!Capsule::schema()->hasTable('years')) {
        Capsule::schema()->create('years', function ($table) {
            $table->id();
            $table->string('name');
            $table->timestamps();
        });
        echo "Created years table\n";
    }

    // Sections table
    if (!Capsule::schema()->hasTable('sections')) {
        Capsule::schema()->create('sections', function ($table) {
            $table->id();
            $table->string('name');
            $table->timestamps();
        });
        echo "Created sections table\n";
    }

    // Groups table
    if (!Capsule::schema()->hasTable('groups')) {
        Capsule::schema()->create('groups', function ($table) {
            $table->id();
            $table->string('name');
            $table->timestamps();
        });
        echo "Created groups table\n";
    }

    // Class romes table
    if (!Capsule::schema()->hasTable('class_romes')) {
        Capsule::schema()->create('class_romes', function ($table) {
            $table->id();
            $table->string('name');
            $table->timestamps();
        });
        echo "Created class_romes table\n";
    }

    // Modules table
    if (!Capsule::schema()->hasTable('modules')) {
        Capsule::schema()->create('modules', function ($table) {
            $table->id();
            $table->string('name');
            $table->timestamps();
        });
        echo "Created modules table\n";
    }

    // Teachers table
    if (!Capsule::schema()->hasTable('teachers')) {
        Capsule::schema()->create('teachers', function ($table) {
            $table->id();
            $table->string('username')->unique();
            $table->string('name');
            $table->string('last');
            $table->date('date_of_birth');
            $table->timestamps();
        });
        echo "Created teachers table\n";
    }

    // Students table
    if (!Capsule::schema()->hasTable('students')) {
        Capsule::schema()->create('students', function ($table) {
            $table->id();
            $table->string('username')->unique();
            $table->string('name');
            $table->string('last');
            $table->date('date_of_birth');
            $table->timestamps();
        });
        echo "Created students table\n";
    }

    // Admins table
    if (!Capsule::schema()->hasTable('admins')) {
        Capsule::schema()->create('admins', function ($table) {
            $table->id();
            $table->string('username')->unique();
            $table->string('name');
            $table->string('last');
            $table->boolean('is_super')->default(false);
            $table->timestamps();
        });
        echo "Created admins table\n";
    }

    // Time tables table
    if (!Capsule::schema()->hasTable('time_tables')) {
        Capsule::schema()->create('time_tables', function ($table) {
            $table->id();
            $table->morphs('timeable');
            $table->timestamps();
        });
        echo "Created time_tables table\n";
    }

    // Days table
    if (!Capsule::schema()->hasTable('days')) {
        Capsule::schema()->create('days', function ($table) {
            $table->id();
            $table->string('name');
            $table->timestamps();
        });
        echo "Created days table\n";
    }

    // Lessens table
    if (!Capsule::schema()->hasTable('lessens')) {
        Capsule::schema()->create('lessens', function ($table) {
            $table->id();
            $table->time('start_time');
            $table->time('end_time');
            $table->enum('type', ['td', 'tp', 'course']);
            $table->timestamps();
        });
        echo "Created lessens table\n";
    }

    // Add foreign keys
    echo "Adding foreign keys...\n";

    if (Capsule::schema()->hasTable('users') && !Capsule::schema()->hasColumn('users', 'key_id')) {
        Capsule::schema()->table('users', function ($table) {
            $table->foreignId('key_id')->nullable()->constrained('keys');
        });
    }

    if (Capsule::schema()->hasTable('baladiyas') && !Capsule::schema()->hasColumn('baladiyas', 'wilaya_id')) {
        Capsule::schema()->table('baladiyas', function ($table) {
            $table->foreignId('wilaya_id')->nullable()->constrained('wilayas');
        });
    }

    if (Capsule::schema()->hasTable('teachers') && !Capsule::schema()->hasColumn('teachers', 'baladiya_id')) {
        Capsule::schema()->table('teachers', function ($table) {
            $table->foreignId('baladiya_id')->nullable()->constrained('baladiyas');
        });
    }

    if (Capsule::schema()->hasTable('students') && !Capsule::schema()->hasColumn('students', 'baladiyas_id')) {
        Capsule::schema()->table('students', function ($table) {
            $table->foreignId('baladiyas_id')->nullable()->constrained('baladiyas');
            $table->foreignId('group_id')->nullable()->constrained('groups');
        });
    }

    if (Capsule::schema()->hasTable('groups') && !Capsule::schema()->hasColumn('groups', 'section_id')) {
        Capsule::schema()->table('groups', function ($table) {
            $table->foreignId('section_id')->nullable()->constrained('sections');
        });
    }

    if (Capsule::schema()->hasTable('sections') && !Capsule::schema()->hasColumn('sections', 'year_id')) {
        Capsule::schema()->table('sections', function ($table) {
            $table->foreignId('year_id')->nullable()->constrained('years');
        });
    }

    if (Capsule::schema()->hasTable('years') && !Capsule::schema()->hasColumn('years', 'department_id')) {
        Capsule::schema()->table('years', function ($table) {
            $table->foreignId('department_id')->nullable()->constrained('departments');
        });
    }

    if (Capsule::schema()->hasTable('days') && !Capsule::schema()->hasColumn('days', 'time_table_id')) {
        Capsule::schema()->table('days', function ($table) {
            $table->foreignId('time_table_id')->nullable()->constrained('time_tables');
        });
    }

    if (Capsule::schema()->hasTable('lessens') && !Capsule::schema()->hasColumn('lessens', 'day_id')) {
        Capsule::schema()->table('lessens', function ($table) {
            $table->foreignId('day_id')->nullable()->constrained('days');
            $table->foreignId('module_id')->nullable()->constrained('modules');
            $table->foreignId('teacher_id')->nullable()->constrained('teachers');
            $table->foreignId('class_rome_id')->nullable()->constrained('class_romes');
        });
    }

    if (Capsule::schema()->hasTable('class_romes') && !Capsule::schema()->hasColumn('class_romes', 'department_id')) {
        Capsule::schema()->table('class_romes', function ($table) {
            $table->foreignId('department_id')->nullable()->constrained('departments');
        });
    }

    // Insert sample data
    echo "Inserting sample data...\n";

    // Insert sample wilaya
    Capsule::table('wilayas')->insertOrIgnore([
        'name' => 'Algiers',
        'created_at' => now(),
        'updated_at' => now()
    ]);

    // Insert sample baladiya
    Capsule::table('baladiyas')->insertOrIgnore([
        'name' => 'Bab El Oued',
        'wilaya_id' => 1,
        'created_at' => now(),
        'updated_at' => now()
    ]);

    // Insert sample department
    Capsule::table('departments')->insertOrIgnore([
        'name' => 'Computer Science',
        'created_at' => now(),
        'updated_at' => now()
    ]);

    // Insert sample modules
    Capsule::table('modules')->insertOrIgnore([
        ['name' => 'Mathematics', 'created_at' => now(), 'updated_at' => now()],
        ['name' => 'Physics', 'created_at' => now(), 'updated_at' => now()],
        ['name' => 'Programming', 'created_at' => now(), 'updated_at' => now()]
    ]);

    // Insert sample class rooms
    Capsule::table('class_romes')->insertOrIgnore([
        ['name' => 'Room A101', 'department_id' => 1, 'created_at' => now(), 'updated_at' => now()],
        ['name' => 'Room A102', 'department_id' => 1, 'created_at' => now(), 'updated_at' => now()]
    ]);

    echo "All tables created and sample data inserted successfully!\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

function now() {
    return date('Y-m-d H:i:s');
}
