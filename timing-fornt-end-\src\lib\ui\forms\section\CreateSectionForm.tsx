"use client";
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import Button from "@/lib/ui/components/global/Buttons/Button";
import { getAllDepartments } from "@/lib/server/actions/department/DepartmentActions";
import { createSection } from "@/lib/server/actions/section/sectionActions";
import { CheckCircle2, AlertCircle } from "lucide-react";

const createSectionSchema = z.object({
  number: z.coerce.number().min(1, "Section number is required"),
  department_id: z.string().min(1, "Department is required"),
  year_id: z.string().min(1, "Year is required"),
});

type CreateSectionFormData = z.infer<typeof createSectionSchema>;

export default function CreateSectionForm({ onSuccess }: { onSuccess?: () => void }) {
  const [departments, setDepartments] = useState<any[]>([]);
  const [years, setYears] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<CreateSectionFormData>({
    resolver: zodResolver(createSectionSchema),
  });

  useEffect(() => {
    getAllDepartments().then((data) => setDepartments(data.departments));
  }, []);

  useEffect(() => {
    const depId = watch("department_id");
    if (depId) {
      const dep = departments.find((d) => d.id === +depId);
      setYears(dep ? dep.years : []);
      setValue("year_id", "");
    }
  }, [watch("department_id"), departments, setValue]);

  const onSubmit = async (data: CreateSectionFormData) => {
    setError(null);
    setSuccess(false);
    try {
      const response = await createSection({
        number: data.number,
        year_id: +data.year_id,
      });
      console.log('Create section response:', response);
      if (response && (response as any).message) {
        setError((response as any).message);
        return;
      }
      setSuccess(true);
      reset();
      onSuccess?.();
    } catch (e: any) {
      console.error('Create section error:', e);
      setError(e.message || "Failed to create section");
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4 w-full max-w-md">
      {error && (
        <div className="flex items-center gap-2 text-red-600 dark:text-red-400 animate-fade-in">
          <AlertCircle size={20} />
          <span>{error}</span>
        </div>
      )}
      {success && (
        <div className="flex items-center gap-2 text-green-600 dark:text-green-400 animate-fade-in">
          <CheckCircle2 size={20} />
          <span>Section created successfully!</span>
        </div>
      )}
      <label>
        Section Number
        <input type="number" {...register("number")} className="input" />
        {errors.number && <span className="text-red-500">{errors.number.message}</span>}
      </label>
      <label>
        Department
        <select {...register("department_id")} className="input">
          <option value="">Select Department</option>
          {departments.map((dep) => (
            <option key={dep.id} value={dep.id}>{dep.name}</option>
          ))}
        </select>
        {errors.department_id && <span className="text-red-500">{errors.department_id.message}</span>}
      </label>
      <label>
        Year
        <select {...register("year_id")} className="input">
          <option value="">Select Year</option>
          {years.map((year) => (
            <option key={year.id} value={year.id}>{year.name}</option>
          ))}
        </select>
        {errors.year_id && <span className="text-red-500">{errors.year_id.message}</span>}
      </label>
      <Button type="submit" mode="filled" disabled={isSubmitting}>
        {isSubmitting ? "Creating..." : "Create Section"}
      </Button>
    </form>
  );
} 