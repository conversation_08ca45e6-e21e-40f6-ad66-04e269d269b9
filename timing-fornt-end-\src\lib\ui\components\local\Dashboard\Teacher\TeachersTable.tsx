import { getTeachers } from "@/lib/server/actions/teacher/getTeachers";
import { DashContentTable, TableTd, TableTdMain, TableThead, TableTr } from "../DashCrudContent";
import TeacherActions from "@/lib/ui/forms/teacher/actions";
import CreateTeacher<PERSON><PERSON> from "@/lib/ui/forms/teacher/createKey";

interface TeachersTableProps {
    page: string;
    search: string;
}

export default async function TeachersTable({ page, search }: TeachersTableProps) {
    const currentPage = parseInt(page) || 1;
    const teachers = await getTeachers(currentPage, search);

    return (
        <>
            <DashContentTable>
                <TableThead list={['Username', 'Key', 'Name', 'Last', 'Date of Birth', 'Grade', 'Research Field', 'Wilaya - Baladiya', 'Email', 'Settings']} />
                <tbody>
                    {teachers?.data.map((teacher) => (
                        <TableTr key={teacher.id}>
                            <TableTdMain value={teacher.username} />
                            <TableTd>
                                {teacher.key?.value || <CreateTeacherKey teacher={teacher} />}
                            </TableTd>
                            <TableTd>
                                {teacher.name}
                            </TableTd>
                            <TableTd>
                                {teacher.last}
                            </TableTd>
                            <TableTd>
                                {teacher.date_of_birth}
                            </TableTd>
                            <TableTd>
                                {teacher.grade || '—'}
                            </TableTd>
                            <TableTd>
                                {teacher.research_field || '—'}
                            </TableTd>
                            <TableTd>
                                {teacher.baladiya?.wilaya?.name || 'No Wilaya'} - {teacher.baladiya?.name || 'No Baladiya'}
                            </TableTd>
                            <TableTd>
                                {teacher.key?.user?.email || 'No Account'}
                            </TableTd>
                            <TableTd>
                                <TeacherActions teacher={teacher} />
                            </TableTd>
                        </TableTr>
                    ))}
                </tbody>
            </DashContentTable>
        </>
    )
}

