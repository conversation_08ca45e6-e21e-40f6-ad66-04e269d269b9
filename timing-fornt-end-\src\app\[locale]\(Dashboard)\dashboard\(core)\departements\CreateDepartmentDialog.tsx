"use client";
import { useState } from "react";
import Button from "@/lib/ui/components/global/Buttons/Button";
import Dialog from "@/lib/ui/components/global/Dialog/Dialog";
import CreateDepartmentForm from "@/lib/ui/forms/department/CreateDepartmentForm";
import { Plus } from "lucide-react";

export default function CreateDepartmentDialog() {
    const [open, setOpen] = useState(false);
    return (
        <>
            <Button mode="filled" icon={<Plus />} onClick={() => setOpen(true)}>
                Create Department
            </Button>
            <Dialog isOpen={open} onClose={() => setOpen(false)} title="Create Department">
                <CreateDepartmentForm onSuccess={() => setOpen(false)} />
            </Dialog>
        </>
    );
}
