"use client";
import { useEffect, useState } from "react";
import { getGroups } from '@/lib/server/actions/group/getGroups';
import { getGroupTiming } from '@/lib/server/actions/groupTiming/GroupTimingActions';
import Button from '@/lib/ui/components/global/Buttons/Button';
import { Search } from 'lucide-react';
import Link from 'next/link';

const STANDARD_SLOTS = [
  { start: "08:00", end: "09:30" },
  { start: "09:30", end: "11:00" },
  { start: "11:00", end: "12:30" },
  { start: "12:30", end: "14:00" },
  { start: "14:00", end: "15:30" },
  { start: "15:30", end: "17:00" },
];
const DAY_LABELS = {
  sat: "Saturday",
  sun: "Sunday",
  mon: "Monday",
  tue: "Tuesday",
  wed: "Wednesday",
  thu: "Thursday",
};
const DAYS = ["sat", "sun", "mon", "tue", "wed", "thu"];

export default function GroupsTimetablesPage({ user }) {
  const [groups, setGroups] = useState([]);
  const [departments, setDepartments] = useState([]);
  const [years, setYears] = useState([]);
  const [filteredGroups, setFilteredGroups] = useState([]);
  const [selectedDepartment, setSelectedDepartment] = useState("");
  const [selectedYear, setSelectedYear] = useState("");
  const [selectedGroup, setSelectedGroup] = useState(null);
  const [timetable, setTimetable] = useState(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Fetch all groups (all pages)
    async function fetchAllGroups() {
      let page = 1;
      let allGroups = [];
      let hasMore = true;
      while (hasMore) {
        const res = await getGroups(page);
        allGroups = allGroups.concat(res.data);
        if (res.next_page_url) {
          page += 1;
        } else {
          hasMore = false;
        }
      }
      setGroups(allGroups);
      // Extract unique departments
      const uniqueDepartments = Array.from(new Set(allGroups.map(g => g.section.year.department.name)));
      setDepartments(uniqueDepartments);
    }
    fetchAllGroups();
  }, []);

  useEffect(() => {
    // When department changes, update years
    if (selectedDepartment) {
      const deptGroups = groups.filter(g => g.section.year.department.name === selectedDepartment);
      const uniqueYears = Array.from(new Set(deptGroups.map(g => g.section.year.name)));
      setYears(uniqueYears);
      setSelectedYear("");
      setFilteredGroups([]);
      setSelectedGroup(null);
      setTimetable(null);
    } else {
      setYears([]);
      setFilteredGroups([]);
      setSelectedYear("");
      setSelectedGroup(null);
      setTimetable(null);
    }
  }, [selectedDepartment]);

  useEffect(() => {
    // When year changes, update groups
    if (selectedYear && selectedDepartment) {
      const yearGroups = groups.filter(
        g => g.section.year.department.name === selectedDepartment && g.section.year.name === selectedYear
      );
      setFilteredGroups(yearGroups);
      setSelectedGroup(yearGroups[0] || null);
      setTimetable(null);
    } else {
      setFilteredGroups([]);
      setSelectedGroup(null);
      setTimetable(null);
    }
  }, [selectedYear, selectedDepartment]);

  useEffect(() => {
    // When group changes, fetch timetable
    if (selectedGroup) {
      setLoading(true);
      getGroupTiming(selectedGroup.id).then(data => {
        setTimetable(data);
        setLoading(false);
      });
    } else {
      setTimetable(null);
    }
  }, [selectedGroup]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Top bar with teacher info and back button */}
      <div className="flex items-center gap-4 p-6 bg-white border-b">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center text-xl font-bold text-gray-600">
            {user?.name?.[0] || "T"}
          </div>
          <div>
            <div className="font-bold">{user?.name || "Teacher"}</div>
            <div className="text-xs text-gray-500">{user?.role || "teacher"}</div>
          </div>
        </div>
        <div className="flex-1" />
        <Button mode="outlined" onClick={() => {
          const locale = window.location.pathname.split('/')[1];
          window.location.href = `/${locale}`;
        }}>
          Back to My Timetable
        </Button>
      </div>
      {/* Main content */}
      <div className="max-w-5xl mx-auto p-8">
        <h1 className="text-2xl font-bold mb-4">All Groups Timetables</h1>
        <div className="mb-6 flex gap-4 items-center">
          <select
            className="border rounded px-2 py-1"
            value={selectedDepartment}
            onChange={e => setSelectedDepartment(e.target.value)}
          >
            <option value="">Select Department</option>
            {departments.map(dept => (
              <option key={dept} value={dept}>{dept}</option>
            ))}
          </select>
          <select
            className="border rounded px-2 py-1"
            value={selectedYear}
            onChange={e => setSelectedYear(e.target.value)}
            disabled={!selectedDepartment}
          >
            <option value="">Select Year</option>
            {years.map(year => (
              <option key={year} value={year}>{year}</option>
            ))}
          </select>
          <select
            className="border rounded px-2 py-1"
            value={selectedGroup?.id || ""}
            onChange={e => {
              const group = filteredGroups.find(g => g.id === Number(e.target.value));
              setSelectedGroup(group);
            }}
            disabled={!selectedYear}
          >
            <option value="">Select Group</option>
            {filteredGroups.map(group => (
              <option key={group.id} value={group.id}>
                Group {group.number} - Section {group.section.number}
              </option>
            ))}
          </select>
        </div>
        {loading && <div className="text-center text-gray-500">Loading timetable...</div>}
        {timetable && (
          <div className="flex flex-col gap-4">
            <table className="min-w-full border border-gray-200 rounded">
              <thead>
                <tr className="bg-gray-100">
                  <th className="p-2 text-left">Day</th>
                  {STANDARD_SLOTS.map(slot => (
                    <th key={slot.start} className="p-2 text-left">{slot.start}–{slot.end}</th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {DAYS.map(dayKey => {
                  const day = timetable.timeTable.days.find(d => d.name.toLowerCase().slice(0,3) === dayKey.slice(0,3));
                  const lessons = day ? day.lessens : [];
                  return (
                    <tr key={dayKey}>
                      <td className="p-2 font-semibold">{DAY_LABELS[dayKey]}</td>
                      {STANDARD_SLOTS.map(slot => {
                        let session = null;
                        if (lessons.length > 0) {
                          session = lessons.find(
                            l => l.start_time.slice(0,5) === slot.start && l.end_time.slice(0,5) === slot.end
                          );
                        }
                        return (
                          <td key={slot.start} className="p-2 align-top">
                            {session ? (
                              <div className="flex flex-col gap-0.5 p-1">
                                <span className="font-semibold text-xs">
                                  {session.module.name}
                                  <span className="ml-1 px-1 py-0.5 rounded bg-blue-100 text-blue-700 text-[10px] font-bold uppercase align-middle">{session.type}</span>
                                </span>
                                <span className="text-[11px] text-gray-500">
                                  {session.teacher.name} {session.teacher.last} &bull; class: {session.class_rome.number}
                                </span>
                              </div>
                            ) : (
                              <div>—</div>
                            )}
                          </td>
                        );
                      })}
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
